import { Component, Input, OnInit, SimpleChanges, ViewChild, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { finalize } from 'rxjs';
import { FlcTableHelperService, FlcUtilService, FlcValidatorService, FlcTableComponent } from 'fl-common-lib';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { NzDrawerService, NzDrawerRef } from 'ng-zorro-antd/drawer';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { endOfDay, format, startOfDay } from 'date-fns';
import { initAllocationDetailHeaders, initAllocationDetailDrawerHeaders } from './allocation-detail-drawer.config';
import {
  PageEditModeEnum,
  AllocationDetailDrawerItem,
  AllocationDetailDrawerRequest,
  AllocationDetailDrawerOptionsResponse,
  SaveExpenseAllocationRequestAllocationLine,
} from '../../../models/payable-report-detail.interface';
import { PayableReportService } from '../../../payable-report.service';

const version = '1.0.1';

@Component({
  selector: 'flss-allocation-detail-drawer',
  templateUrl: './allocation-detail-drawer.component.html',
  styleUrls: ['./allocation-detail-drawer.component.scss'],
})
export class AllocationDetailDrawerComponent implements OnInit {
  @ViewChild('drawerTpl') drawerTpl!: TemplateRef<any>;
  @ViewChild(FlcTableComponent) flcTable!: FlcTableComponent;

  @Input() editMode: PageEditModeEnum = PageEditModeEnum.add;
  @Input() detailInfo?: any;
  @Input() baseinfoForm?: any;
  @Input() totalAllocationAmount = 0; // 合计的分摊金额

  // 分摊金额数据
  private allocationAmountData: {
    totalAmount: number;
    categoryOneAmount: number;
    categoryTwoList: Array<{
      customer_id: number;
      order_uuid: string;
      style_code: string;
      amortization_amount: number;
    }>;
  } = {
    totalAmount: 0,
    categoryOneAmount: 0,
    categoryTwoList: [],
  };

  disabledBtn = false;
  pageEditModeEnum = PageEditModeEnum;

  // 统计数据
  statisticData: any = {
    total_inbound_quantity: 0,
    total_amortization_amount: 0,
    total_amortization_rate: 0,
  };

  // 防止无限循环的标志
  private isUpdatingFormValues = false;

  // 表单定义
  allocationForm: FormGroup = this._fb.group({
    allocation_detail_list: this._fb.array([]),
  });

  get allocation_detail_list(): FormArray {
    return this.allocationForm?.get('allocation_detail_list') as FormArray;
  }

  // 主表格数据
  listOfData: AllocationDetailDrawerItem[] = [];

  // 抽屉搜索参数
  searchData: any = {
    inbound_time: null,
    customer_id: null,
    order_uuid: null,
    style_uuid: null,
    keyword: null,
  };

  // 下拉选项
  optionsList: AllocationDetailDrawerOptionsResponse = {
    customer: [],
    order: [],
    style: [],
  };

  // 抽屉选中数据
  selectData: AllocationDetailDrawerItem[] = [];

  // 抽屉引用
  private drawerRef?: NzDrawerRef;

  // 分摊相关数据
  totalInboundQuantity = 0; // 合计入库数量

  constructor(
    private _fb: FormBuilder,
    private _drawerService: NzDrawerService,
    private _noticeService: NzNotificationService,
    private _messageService: NzMessageService,
    private _tableHelper: FlcTableHelperService,
    private _flcValidatorService: FlcValidatorService,
    private _flcUtil: FlcUtilService,
    private _service: PayableReportService,
    private _cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.headers = this._tableHelper.getFlcTableHeaderConfig<any>(initAllocationDetailHeaders(), version);
    this.getRenderHeaders();
    // this.loadData();
    this.updateStatisticData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes.detailInfo && changes.detailInfo.currentValue) {
      this.loadData();
    }
  }

  /**
   * 加载主表格数据
   * 从getExpenseAllocationDetail接口获取数据时，保持接口返回的原始分摊金额和分摊比例
   * 不进行重新计算，因为这些数据可能已经被用户修改过
   */
  private loadData() {
    if (this.detailInfo && this.detailInfo.allocation_lines) {
      // 加载详情中的分摊明细数据
      this.listOfData = this.detailInfo.allocation_lines.map((item: any) => ({
        id: item.id || '0',
        expense_allocation_id: item.expense_allocation_id || '0',
        customer_id: item.customer_id || '',
        customer_name: item.customer_name || '',
        order_code: item.order_code || '',
        order_uuid: item.order_uuid || '',
        product_inbound_id: item.product_inbound_id || '',
        product_inbound_line_id: item.product_inbound_line_id || '',
        style_code: item.style_code || '',
        product_name: item.product_name || '',
        inbound_quantity: item.inbound_quantity || '0',
        inbound_time: item.inbound_time || '',
        allocation_amount: item.amortization_amount || '0', // 分摊金额 - 保持接口返回的原始值
        allocation_ratio: item.amortization_rate || '0', // 分摊比例 - 保持接口返回的原始值
      }));

      // 更新表单数组，但不重新计算分摊数据（保持接口返回的原始数据）
      // 只有在用户后续修改分摊金额组件数据时才会重新计算
      this.updateFormArray(false);
    } else {
      // 如果没有详情数据，使用空数组
      this.listOfData = [];
    }
  }

  /**
   * 重新获取渲染表头
   */
  headers: any[] = [];
  renderHeaders: any[] = [];
  btnHighLight = false;
  renderScrollX = '100%';
  tableHeight = 0;

  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    this._tableHelper
      .openTableHeaderMidifyDialog<any>(shadow, event.target as HTMLElement, 'start')
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = initAllocationDetailHeaders().find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          this._tableHelper.saveFlcTableHeaderConfig(this.headers, version);
        }
      });
  }

  onResize({ width }: NzResizeEvent, col: string): void {
    this.headers = this._tableHelper.tableResize<any>(width, col, this.headers, version);
    this.getRenderHeaders();
  }

  private getRenderHeaders() {
    this.renderHeaders = this._tableHelper.getRenderHeader<any>(this.headers);
    this.renderScrollX = this._tableHelper.getRenderHeaderScrollWidth(this.renderHeaders) + 'px';
  }

  /**
   * 选择明细
   */
  onAddDetail() {
    this.drawerRef = this._drawerService.create({
      nzTitle: '选择分摊明细',
      nzContent: this.drawerTpl,
      nzHeight: window.innerHeight * 0.8,
      nzMaskClosable: false,
      nzWrapClassName: 'modal-outer-order-selector',
      nzPlacement: 'bottom',
    });

    // 加载抽屉选项数据
    this.loadDrawerOptions();
    // 加载抽屉列表数据
    this.getDrawerList();
  }

  /**
   * 加载抽屉选项数据
   */
  private loadDrawerOptions() {
    this._service.getAllocationDetailDrawerOptions().subscribe({
      next: (res) => {
        if (res.code === 200) {
          this.optionsList = res.data;
        }
      },
      error: (error) => {
        this._messageService.error('获取选项数据失败');
        console.error('获取选项数据失败:', error);
      },
    });
  }

  // 设置表头信息
  tableHeader = initAllocationDetailDrawerHeaders();

  // 表格配置项
  tableConfig = {
    version: '1.0.1',
    tableName: 'allocationDetailDrawerTable',
    translateName: '',
    dataList: <AllocationDetailDrawerItem[]>[], //表格数据
    hasCheckbox: true, //是否显示选中功能
    detailBtn: false,
    count: 0, //数据总数量
    pageIndex: 1, //当前页码
    pageSize: 20, //当前每页数量
    loading: false, //是否加载中
    clearSelect: false,
    height: 500,
    uniqueId: 'unique_key',
    actionWidth: '80px',
  };

  /**
   * 搜索
   */
  onSearch() {
    this.tableConfig.pageIndex = 1;
    this.getDrawerList();
  }

  /**
   * 重置搜索
   */
  resetList() {
    this.searchData = {
      inbound_time: null,
      customer_id: null,
      order_uuid: null,
      style_uuid: null,
      keyword: null,
    };
    this.onSearch();
  }

  /**
   * 获取抽屉列表数据
   */
  private getDrawerList() {
    this.tableConfig.loading = true;

    const params: AllocationDetailDrawerRequest = {
      page: this.tableConfig.pageIndex.toString(),
      page_size: this.tableConfig.pageSize.toString(),
      keyword: this.searchData.keyword || undefined,
      customer_id: this.searchData.customer_id || undefined,
      order_uuid: this.searchData.order_uuid || undefined,
      style_uuid: this.searchData.style_uuid || undefined,
    };

    // 处理日期范围，转换为时间戳
    if (this.searchData.inbound_time && this.searchData.inbound_time.length === 2) {
      params.inbound_start_time = startOfDay(this.searchData.inbound_time[0]).getTime().toString();
      params.inbound_end_time = endOfDay(this.searchData.inbound_time[1]).getTime().toString();
    }

    this._service
      .getAllocationDetailDrawerList(params)
      .pipe(finalize(() => (this.tableConfig.loading = false)))
      .subscribe({
        next: (res) => {
          if (res.code === 200) {
            this.tableConfig.dataList = res.data.data || [];
            this.tableConfig.count = parseInt(res.data.total || '0');
            this.tableConfig = { ...this.tableConfig };
          }
        },
        error: (error) => {
          this._messageService.error('获取分摊明细列表失败');
          console.error('获取分摊明细列表失败:', error);
        },
      });
  }

  /**
   * 页码变化
   */
  indexChanges(pageIndex: number) {
    this.tableConfig.pageIndex = pageIndex;
    this.getDrawerList();
  }

  /**
   * 页大小变化
   */
  sizeChanges(pageSize: number) {
    this.tableConfig.pageSize = pageSize;
    this.tableConfig.pageIndex = 1;
    this.getDrawerList();
  }

  /**
   * 获取选中数据
   */
  getCount(selectData: { count: number; list: AllocationDetailDrawerItem[] }) {
    this.selectData = selectData.list || [];
  }

  /**
   * 单个选择
   * @description unique_key是现在作为唯一id了
   */
  onSelect(item: AllocationDetailDrawerItem) {
    // 添加到主表格数据
    if (!this.listOfData.find((data) => data.unique_key === item.unique_key)) {
      this.listOfData = [...this.listOfData, item];
      this.updateFormArray(true); // 添加新数据时重新计算分摊数据
      this._messageService.success('已选择1条数据');
    } else {
      this._messageService.warning('该数据已存在');
    }

    // 关闭抽屉
    this.drawerRef?.close();
  }

  /**
   * 清空选中
   */
  onClear() {
    this.selectData = [];
    this.flcTable?.clearAllSelected();
  }

  /**
   * 批量选择确认
   * @description unique_key是现在作为唯一id了
   */
  handleOk() {
    // 添加选中的数据到主表格
    this.selectData.forEach((item) => {
      if (!this.listOfData.find((data) => data.unique_key === item.unique_key)) {
        this.listOfData.push(item);
      }
    });
    this.listOfData = [...this.listOfData];
    this.updateFormArray(true); // 批量添加新数据时重新计算分摊数据

    // 关闭抽屉
    this.drawerRef?.close();
    this._messageService.success(`已选择${this.selectData.length}条数据`);
  }

  /**
   * 从主表格中删除项目
   */
  onRemove(index: number) {
    // 从 listOfData 中删除对应的项目
    this.listOfData.splice(index, 1);
    this.listOfData = [...this.listOfData]; // 触发变更检测

    // 从表单数组中删除对应的控件
    this.allocation_detail_list.removeAt(index);

    // 更新统计数据
    this.updateStatisticData();
  }

  /**
   * 创建表单控件
   */
  private createFormControl(item: AllocationDetailDrawerItem) {
    return this._fb.group({
      id: [item.id || '0'],
      expense_allocation_id: [item.expense_allocation_id || '0'], // 添加expense_allocation_id字段
      customer_id: [item.customer_id || ''],
      customer_name: [item.customer_name || ''],
      order_code: [item.order_code || ''],
      order_uuid: [item.order_uuid || ''],
      style_code: [item.style_code || ''],
      product_name: [item.product_name || ''],
      inbound_quantity: [item.inbound_quantity || ''],
      inbound_time: [item.inbound_time || item.inbound_date || ''], // 统一使用 inbound_time
      allocation_amount: [item.allocation_amount || '0.0000'],
      allocation_ratio: [item.allocation_ratio || '0.00'],
      product_inbound_line_id: [item.product_inbound_line_id || ''],
      product_inbound_id: [item.product_inbound_id || ''],
    });
  }

  /**
   * 更新表单数组
   * @param shouldCalculate 是否需要重新计算分摊数据，默认为true
   */
  private updateFormArray(shouldCalculate = true) {
    // 只有在需要计算时才重新计算分摊比例和分摊金额
    if (shouldCalculate) {
      this.calculateAllocationData();
    }
    
    // // 清空现有的表单数组
    // while (this.allocation_detail_list.length !== 0) {
    //   this.allocation_detail_list.removeAt(0);
    // }
    this.allocation_detail_list.clear()

    // 添加新的表单控件
    this.listOfData.forEach((item, index) => {
      const formControl = this.createFormControl(item);
      this.allocation_detail_list.push(formControl);

      // 添加分摊金额变化监听
      formControl.get('allocation_amount')?.valueChanges.subscribe((value: any) => {
        if (value !== null && value !== undefined && !this.isUpdatingFormValues) {
          this.onAllocationAmountChange(index, value);
        }
      });

      // 添加分摊比例变化监听
      formControl.get('allocation_ratio')?.valueChanges.subscribe((value: any) => {
        if (value !== null && value !== undefined && !this.isUpdatingFormValues) {
          this.onAllocationRatioChange(index, value);
        }
      });
    });

    // 更新统计数据
    this.updateStatisticData();
  }

  /**
   * 计算分摊比例和分摊金额
   */
  private calculateAllocationData() {
    if (this.listOfData.length === 0) return;

    // 计算合计入库数，使用 FlcUtilService 的方法
    const totalInboundQuantity = this.listOfData.reduce((sum, item) => {
      return this._flcUtil.accAdd(sum, item.inbound_quantity ?? 0);
    }, 0);

    // 为每条数据计算分摊比例和分摊金额
    this.listOfData.forEach((item) => {
      // 计算分摊比例：该条入库数/合计入库数 (保留2位小数，不带%符号)
      const ratio = totalInboundQuantity > 0 ? this._flcUtil.accDiv(item.inbound_quantity ?? 0, totalInboundQuantity) : 0;
      const ratioPercent = this._flcUtil.accMul(ratio, 100);
      item.allocation_ratio = this._flcUtil.toFixed(ratioPercent, 2).toString();

      // 计算分摊金额：使用新的复杂公式
      const allocationAmount = this.calculateItemAllocationAmount(item, ratio);
      item.allocation_amount = this._flcUtil.toFixed(allocationAmount, 4).toString();
    });
  }

  /**
   * 计算单个项目的分摊金额
   * 公式：分摊金额 = (第一类金额 * 分摊比例) + (匹配的第二类金额 * 当前订单占用比例)
   */
  private calculateItemAllocationAmount(item: AllocationDetailDrawerItem, ratio: number): number {
    // 第一部分：第一类金额 * 分摊比例
    const categoryOnePart = this._flcUtil.accMul(this.allocationAmountData.categoryOneAmount, ratio);

    // 第二部分：匹配的第二类金额 * 当前订单占用比例
    let categoryTwoPart = 0;

    // 查找匹配的第二类金额（客户ID、订单号、款号任意一个相同）
    const matchedCategoryTwo = this.allocationAmountData.categoryTwoList.find(
      (categoryItem) =>
        categoryItem.customer_id === item.customer_id ||
        categoryItem.order_uuid === item.order_uuid ||
        categoryItem.style_code === item.style_code
    );

    if (matchedCategoryTwo) {
      // 计算当前订单占用比例：当前行入库数 / 相同订单号所有行的入库数总和
      const orderOccupancyRatio = this.calculateOrderOccupancyRatio(item);
      categoryTwoPart = this._flcUtil.accMul(matchedCategoryTwo.amortization_amount, orderOccupancyRatio);
    }

    // 返回两部分的总和
    return this._flcUtil.accAdd(categoryOnePart, categoryTwoPart);
  }

  /**
   * 计算当前订单占用比例
   * 当前行入库数 / 相同订单号所有行的入库数总和
   */
  private calculateOrderOccupancyRatio(currentItem: AllocationDetailDrawerItem): number {
    // 找到相同订单号的所有行
    const sameOrderItems = this.listOfData.filter((item) => item.order_uuid === currentItem.order_uuid);

    // 计算相同订单号所有行的入库数总和
    const totalSameOrderInboundQty = sameOrderItems.reduce((sum, item) => {
      const inboundQty =
        (typeof item.inbound_quantity === 'number' ? item.inbound_quantity : parseFloat(item.inbound_quantity || '0')) || 0;
      return this._flcUtil.accAdd(sum, inboundQty);
    }, 0);

    // 返回占用比例
    return totalSameOrderInboundQty > 0 ? this._flcUtil.accDiv(currentItem.inbound_quantity ?? 0, totalSameOrderInboundQty) : 0;
  }

  /**
   * 分摊金额变更处理
   */
  onAllocationAmountChange(index: number, newAmount: string) {
    try {
      // 参数验证
      if (typeof index !== 'number' || index < 0 || index >= this.listOfData.length) {
        console.error('Invalid index:', index, 'listOfData length:', this.listOfData.length);
        return;
      }

      if (!this.listOfData[index]) {
        console.error('Data item not found at index:', index);
        return;
      }

      // 数值处理
      const amount = parseFloat(newAmount) || 0;
      if (isNaN(amount) || amount < 0) {
        console.warn('Invalid amount:', newAmount);
        return;
      }

      // 更新数据源
      this.listOfData[index].allocation_amount = this._flcUtil.toFixed(amount, 4).toString();

      // 根据需求5：修改分摊金额后不反向计算分摊比例，分摊比例保持不变

      // 更新表单控件 (不触发事件，避免循环调用)
      const control = this.allocation_detail_list.at(index);
      if (control) {
        this.isUpdatingFormValues = true;
        control.get('allocation_amount')?.setValue(this.listOfData[index].allocation_amount, { emitEvent: false });
        // 不更新分摊比例，保持原值
        this.isUpdatingFormValues = false;
      } else {
        console.error('Form control not found at index:', index);
      }

      // 更新统计数据
      this.updateStatisticData();
    } catch (error) {
      console.error('Error in onAllocationAmountChange:', error);
    }
  }

  /**
   * 分摊比例变更处理
   */
  onAllocationRatioChange(index: number, newRatio: number) {
    try {
      // 参数验证
      if (typeof index !== 'number' || index < 0 || index >= this.listOfData.length) {
        console.error('Invalid index:', index, 'listOfData length:', this.listOfData.length);
        return;
      }

      if (!this.listOfData[index]) {
        console.error('Data item not found at index:', index);
        return;
      }

      // 数值处理
      const ratio = parseFloat(newRatio.toString()) || 0;
      if (isNaN(ratio) || ratio < 0 || ratio > 100) {
        console.warn('Invalid ratio:', newRatio);
        return;
      }

      // 更新数据源 (保留2位小数，不带%符号)
      this.listOfData[index].allocation_ratio = this._flcUtil.toFixed(ratio, 2).toString();

      // 根据需求5：修改分摊比例后不反向计算分摊金额，分摊金额保持不变

      // 更新表单控件 (不触发事件，避免循环调用)
      const control = this.allocation_detail_list.at(index);
      if (control) {
        this.isUpdatingFormValues = true;
        control.get('allocation_ratio')?.setValue(this.listOfData[index].allocation_ratio, { emitEvent: false });
        // 不更新分摊金额，保持原值
        this.isUpdatingFormValues = false;
      } else {
        console.error('Form control not found at index:', index);
      }

      // 更新统计数据
      this.updateStatisticData();
    } catch (error) {
      console.error('Error in onAllocationRatioChange:', error);
    }
  }

  /**
   * 设置合计分摊金额
   */
  setTotalAllocationAmount(amount: number) {
    this.totalAllocationAmount = amount;
    this.updateFormArray(true); // 重新计算分摊数据
  }

  /**
   * 设置分摊金额数据
   */
  setAllocationAmountData(data: {
    totalAmount: number;
    categoryOneAmount: number;
    categoryTwoList: Array<{
      customer_id: number;
      order_uuid: string;
      style_code: string;
      amortization_amount: number;
    }>;
  }) {
    this.allocationAmountData = data;
    this.totalAllocationAmount = data.totalAmount;
    this.updateFormArray(true); // 重新计算分摊数据
  }

  /**
   * 获取分摊明细数据
   */
  getAllocationDetailData() {
    return this.listOfData.map((item) => ({
      ...item,
      allocation_amount: parseFloat(item.allocation_amount || '0'),
      allocation_ratio: this._flcUtil.accDiv(parseFloat(item.allocation_ratio?.replace('%', '') || '0'), 100),
    }));
  }

  /**
   * 获取保存时需要的 allocation_lines 数据格式
   */
  getAllocationLines(): SaveExpenseAllocationRequestAllocationLine[] {
    return this.allocation_detail_list.controls.map((control: any) => {
      const value = control.value;
      return {
        id: typeof value.id === 'number' ? value.id : parseInt(value.id) || 0, // 转换为数字
        expense_allocation_id:
          typeof value.expense_allocation_id === 'number' ? value.expense_allocation_id : parseInt(value.expense_allocation_id) || 0, // 使用表单中的expense_allocation_id值
        customer_id: typeof value.customer_id === 'number' ? value.customer_id : parseInt(value.customer_id) || 0,
        customer_name: value.customer_name,
        order_code: value.order_code,
        order_uuid: value.order_uuid,
        product_inbound_id:
          typeof value.product_inbound_id === 'number' ? value.product_inbound_id : parseInt(value.product_inbound_id) || 0,
        product_inbound_line_id:
          typeof value.product_inbound_line_id === 'number' ? value.product_inbound_line_id : parseInt(value.product_inbound_line_id) || 0,
        style_code: value.style_code,
        product_name: value.product_name,
        inbound_quantity: typeof value.inbound_quantity === 'number' ? value.inbound_quantity : parseFloat(value.inbound_quantity) || 0,
        inbound_time: typeof value.inbound_time === 'number' ? value.inbound_time : parseInt(value.inbound_time) || 0,
        amortization_amount: value.allocation_amount, // 使用 allocation_amount
        amortization_rate: value.allocation_ratio, // 使用 allocation_ratio
      };
    });
  }

  /**
   * 更新统计数据
   */
  private updateStatisticData() {
    // 初始化统计数据
    this.statisticData = {
      total_inbound_quantity: 0,
      total_amortization_amount: 0,
      total_amortization_rate: 0,
    };

    // 计算合计
    this.allocation_detail_list.controls.forEach((control: any) => {
      const inboundQuantity = parseFloat(control.get('inbound_quantity')?.value || '0');
      const allocationAmount = parseFloat(control.get('allocation_amount')?.value || '0');
      const allocationRatio = parseFloat(control.get('allocation_ratio')?.value || '0');

      // 累加入库数量
      this.statisticData.total_inbound_quantity = this._flcUtil.accAdd(this.statisticData.total_inbound_quantity, inboundQuantity);

      // 累加分摊金额
      this.statisticData.total_amortization_amount = this._flcUtil.accAdd(this.statisticData.total_amortization_amount, allocationAmount);

      // 累加分摊比例
      this.statisticData.total_amortization_rate = this._flcUtil.accAdd(this.statisticData.total_amortization_rate, allocationRatio);
    });
  }
}
